/**
 * ReplyPal - Centralized Configuration
 * Contains all environment settings, API URLs, and default configurations
 */

// Make configuration available globally for Chrome extension compatibility
window.ReplyPalConfig = window.ReplyPalConfig || {};

/**
 * API URLs for different environments
 */
window.ReplyPalConfig.API_URLS = {
  local: 'http://localhost:8000',
  dev: 'https://dev-api.replypal.com',
  prod: 'https://n9dk65q3fd.execute-api.us-east-1.amazonaws.com/production'
};

/**
 * Default settings for the extension
 */
window.ReplyPalConfig.DEFAULT_SETTINGS = {
  environment: 'local', // local, dev, prod
  saveHistory: true,
  useMockApi: false,
  hideFloatingIconGlobally: false,
  hiddenDomains: []
};

/**
 * External URLs
 */
window.ReplyPalConfig.EXTERNAL_URLS = {
  subscription: 'http://replypal-subscription.s3-website-us-east-1.amazonaws.com/index.html'
};

/**
 * Usage limits configuration
 */
window.ReplyPalConfig.USAGE_LIMITS = {
  free: {
    dailyResponses: 5,
    tokensPerMonth: 500
  },
  basic: {
    dailyResponses: 50,
    tokensPerMonth: 1000
  }
};

/**
 * Get API URL for a specific environment
 * @param {string} environment - The environment (local, dev, prod)
 * @returns {string} The API URL for the specified environment
 */
window.ReplyPalConfig.getApiUrl = function(environment) {
  return window.ReplyPalConfig.API_URLS[environment] || window.ReplyPalConfig.API_URLS.prod;
};

/**
 * Get current API URL based on stored settings
 * @returns {Promise<string>} The current API URL
 */
window.ReplyPalConfig.getCurrentApiUrl = function() {
  return new Promise((resolve) => {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.local.get('settings', (data) => {
        const settings = data.settings || window.ReplyPalConfig.DEFAULT_SETTINGS;
        const environment = settings.environment || 'prod';
        resolve(window.ReplyPalConfig.getApiUrl(environment));
      });
    } else {
      // Fallback for non-extension contexts
      const environment = localStorage.getItem('replypal_environment') || 'prod';
      resolve(window.ReplyPalConfig.getApiUrl(environment));
    }
  });
};

/**
 * Get current settings with defaults
 * @returns {Promise<Object>} The current settings object
 */
window.ReplyPalConfig.getCurrentSettings = function() {
  return new Promise((resolve) => {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.local.get('settings', (data) => {
        const settings = { ...window.ReplyPalConfig.DEFAULT_SETTINGS, ...(data.settings || {}) };
        resolve(settings);
      });
    } else {
      // Fallback for non-extension contexts
      try {
        const storedSettings = localStorage.getItem('replypal_settings');
        const settings = storedSettings ? JSON.parse(storedSettings) : {};
        resolve({ ...window.ReplyPalConfig.DEFAULT_SETTINGS, ...settings });
      } catch (error) {
        console.error('Error parsing stored settings:', error);
        resolve(window.ReplyPalConfig.DEFAULT_SETTINGS);
      }
    }
  });
};

/**
 * Update settings
 * @param {Object} newSettings - The settings to update
 * @returns {Promise<void>}
 */
window.ReplyPalConfig.updateSettings = function(newSettings) {
  return new Promise((resolve) => {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.local.get('settings', (data) => {
        const currentSettings = data.settings || {};
        const updatedSettings = { ...currentSettings, ...newSettings };
        chrome.storage.local.set({ settings: updatedSettings }, () => {
          resolve();
        });
      });
    } else {
      // Fallback for non-extension contexts
      try {
        const storedSettings = localStorage.getItem('replypal_settings');
        const currentSettings = storedSettings ? JSON.parse(storedSettings) : {};
        const updatedSettings = { ...currentSettings, ...newSettings };
        localStorage.setItem('replypal_settings', JSON.stringify(updatedSettings));
        resolve();
      } catch (error) {
        console.error('Error updating settings:', error);
        resolve();
      }
    }
  });
};

/**
 * Initialize default settings if they don't exist
 * @returns {Promise<void>}
 */
window.ReplyPalConfig.initializeSettings = function() {
  return new Promise((resolve) => {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.local.get('settings', (data) => {
        if (!data.settings) {
          chrome.storage.local.set({
            settings: window.ReplyPalConfig.DEFAULT_SETTINGS
          }, () => {
            resolve();
          });
        } else {
          // Ensure new settings exist in existing settings
          const settings = data.settings;
          let needsUpdate = false;

          // Check for missing settings and add them with defaults
          Object.keys(window.ReplyPalConfig.DEFAULT_SETTINGS).forEach(key => {
            if (settings[key] === undefined) {
              settings[key] = window.ReplyPalConfig.DEFAULT_SETTINGS[key];
              needsUpdate = true;
            }
          });

          // Force environment to 'prod' if it's currently 'local' (migration fix)
          if (settings.environment === 'local') {
            console.log('ReplyPal: Migrating environment from local to prod');
            settings.environment = 'prod';
            needsUpdate = true;
          }

          if (needsUpdate) {
            chrome.storage.local.set({ settings: settings }, () => {
              resolve();
            });
          } else {
            resolve();
          }
        }
      });
    } else {
      // Fallback for non-extension contexts
      try {
        const storedSettings = localStorage.getItem('replypal_settings');
        if (!storedSettings) {
          localStorage.setItem('replypal_settings', JSON.stringify(window.ReplyPalConfig.DEFAULT_SETTINGS));
        }
        resolve();
      } catch (error) {
        console.error('Error initializing settings:', error);
        resolve();
      }
    }
  });
};

/**
 * Get subscription URL
 * @returns {string} The subscription URL
 */
window.ReplyPalConfig.getSubscriptionUrl = function() {
  return window.ReplyPalConfig.EXTERNAL_URLS.subscription;
};

/**
 * Get usage limits for a specific tier
 * @param {string} tier - The subscription tier (free, basic)
 * @returns {Object} The usage limits for the tier
 */
window.ReplyPalConfig.getUsageLimits = function(tier) {
  return window.ReplyPalConfig.USAGE_LIMITS[tier] || window.ReplyPalConfig.USAGE_LIMITS.free;
};

// Log that configuration has been loaded
console.log('ReplyPal Configuration loaded');
